{"version": 3, "file": "NodeHttpDependencyTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/NodeHttpDependencyTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\nimport http = require(\"http\");\r\nimport https = require(\"https\");\r\n\r\n/** \r\n * An interface describing the standard Node.js options parameter\r\n * for http requests.\r\n */\r\nexport interface httpRequestOptions {\r\n    protocol?: string;\r\n    host?: string;\r\n    hostname?: string;\r\n    family?: number;\r\n    port?: number;\r\n    localAddress?: string;\r\n    socketPath?: string;\r\n    method?: string;\r\n    path?: string;\r\n    headers?: { [key: string]: any };\r\n    auth?: string;\r\n}\r\n/**\r\n * Object encapsulating information about the outgoing request\r\n */\r\nexport interface NodeHttpDependencyTelemetry extends Telemetry\r\n{\r\n    /**\r\n     * Request options that will be used to instrument outgoing request\r\n     */\r\n    options: string | httpRequestOptions;\r\n\r\n    /**\r\n     * Outgoing HTTP request object\r\n     */\r\n    request: http.ClientRequest;\r\n}"]}