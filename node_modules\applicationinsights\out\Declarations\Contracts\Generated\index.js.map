{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/index.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;;AACb,yDAA+D;AAC/D,iCAAuC;AACvC,qDAA2D;AAC3D,iCAAuC;AACvC,2CAAiD;AACjD,mDAAyD;AACzD,qCAA2C;AAC3C,yCAA+C;AAC/C,2CAAiD;AACjD,mDAAyD;AACzD,yDAA+D;AAC/D,+CAAqD;AACrD,6CAAmD;AACnD,iDAAuD;AACvD,iEAAuE;AACvE,+CAAqD;AACrD,mDAAyD;AACzD,6CAAmD", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\nexport import AvailabilityData = require(\"./AvailabilityData\");\r\nexport import Base = require(\"./Base\");\r\nexport import ContextTagKeys = require(\"./ContextTagKeys\");\r\nexport import Data = require(\"./Data\");\r\nexport import DataPoint = require(\"./DataPoint\");\r\nexport import DataPointType = require(\"./DataPointType\");\r\nexport import Domain = require(\"./Domain\");\r\nexport import Envelope = require(\"./Envelope\");\r\nexport import EventData = require(\"./EventData\");\r\nexport import ExceptionData = require(\"./ExceptionData\");\r\nexport import ExceptionDetails = require(\"./ExceptionDetails\");\r\nexport import MessageData = require(\"./MessageData\");\r\nexport import MetricData = require(\"./MetricData\");\r\nexport import PageViewData = require(\"./PageViewData\");\r\nexport import RemoteDependencyData = require(\"./RemoteDependencyData\");\r\nexport import RequestData = require(\"./RequestData\");\r\nexport import SeverityLevel = require(\"./SeverityLevel\");\r\nexport import StackFrame = require(\"./StackFrame\");\r\n"]}