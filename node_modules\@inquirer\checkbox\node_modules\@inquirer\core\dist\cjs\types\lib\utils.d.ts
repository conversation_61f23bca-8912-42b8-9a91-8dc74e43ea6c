/**
 * Force line returns at specific width. This function is ANSI code friendly and it'll
 * ignore invisible codes during width calculation.
 * @param {string} content
 * @param {number} width
 * @return {string}
 */
export declare function breakLines(content: string, width: number): string;
/**
 * Returns the width of the active readline, or 80 as default value.
 * @returns {number}
 */
export declare function readlineWidth(): number;
