{"version": 3, "file": "EnvelopeTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/EnvelopeTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Telemetry Envelope\r\n */\r\nexport interface EnvelopeTelemetry {\r\n    /**\r\n    * Envelope version. For internal use only. By assigning this the default, it will not be serialized within the payload unless changed to a value other than #1.\r\n    */\r\n    ver: number;\r\n    /**\r\n    * Type name of telemetry data item.\r\n    */\r\n    name: string;\r\n\r\n    /**\r\n     * Event date time when telemetry item was created. This is the wall clock time on the client when the event was generated. There is no guarantee that the client's time is accurate. This field must be formatted in UTC ISO 8601 format, with a trailing 'Z' character, as described publicly on https://en.wikipedia.org/wiki/ISO_8601#UTC. Note: the number of decimal seconds digits provided are variable (and unspecified). Consumers should handle this, i.e. managed code consumers should not use format 'O' for parsing as it specifies a fixed length. Example: 2009-06-15T13:45:30.0000000Z.\r\n     */\r\n    time: string;\r\n\r\n    /**\r\n     * Sampling rate used in application. This telemetry item represents 1 / sampleRate actual telemetry items.\r\n     */\r\n    sampleRate: number;\r\n\r\n    /**\r\n     * Sequence field used to track absolute order of uploaded events.\r\n     */\r\n    seq: string;\r\n\r\n    /**\r\n     * The application's instrumentation key. The key is typically represented as a GUID, but there are cases when it is not a guid. No code should rely on iKey being a GUID. Instrumentation key is case insensitive.\r\n     */\r\n    iKey: string;\r\n\r\n    /**\r\n     * Key/value collection of context properties. See ContextTagKeys for information on available properties.\r\n     */\r\n    tags: Tags & Tags[];\r\n    /**\r\n     * Part B Data\r\n     */\r\n    data: DataTelemetry;\r\n\r\n}\r\n\r\n/**\r\n * Envelope Data\r\n */\r\nexport interface DataTelemetry {\r\n    /**\r\n     * Telemetry type used for part B\r\n     */\r\n    baseType: string;\r\n    /**\r\n     * Based on schema for part B\r\n     */\r\n    baseData?: {\r\n        [key: string]: any;\r\n    };\r\n}\r\n\r\n/**\r\n * Envelope Tags\r\n */\r\nexport interface Tags {\r\n    [key: string]: any;\r\n}\r\n"]}