{"version": 3, "file": "Constants.js", "sourceRoot": "", "sources": ["../../Declarations/Constants.ts"], "names": [], "mappings": ";;AAEa,QAAA,uBAAuB,GAAG,sCAAsC,CAAC;AACjE,QAAA,4BAA4B,GAAG,sCAAsC,CAAC;AACtE,QAAA,wBAAwB,GAAG,8BAA8B,CAAC;AAEvE,IAAY,iBAmBX;AAnBD,WAAY,iBAAiB;IACzB,SAAS;IACT,kEAA4C,CAAA;IAE5C,MAAM;IACN,6EAAuD,CAAA;IAEvD,UAAU;IACV,yEAAoD,CAAA;IACpD,wFAAmE,CAAA;IACnE,iFAA2D,CAAA;IAE3D,aAAa;IACb,oFAA+D,CAAA;IAC/D,mGAA8E,CAAA;IAC9E,4FAAsE,CAAA;IAEtE,YAAY;IACZ,6EAAwD,CAAA;AAC5D,CAAC,EAnBW,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QAmB5B;AAED,IAAY,kBAYX;AAZD,WAAY,kBAAkB;IAC1B,SAAS;IACT,oFAA6D,CAAA;IAC7D,mEAA4C,CAAA;IAE5C,MAAM;IACN,8EAAuD,CAAA;IACvD,sFAA+D,CAAA;IAE/D,WAAW;IACX,+FAAwE,CAAA;IACxE,6GAAsF,CAAA;AAC1F,CAAC,EAZW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAY7B;AAAA,CAAC;AAEF;;GAEG;AACU,QAAA,8BAA8B;IACvC,GAAC,kBAAkB,CAAC,cAAc,IAAG,iBAAiB,CAAC,cAAc;IACrE,GAAC,kBAAkB,CAAC,YAAY,IAAG,iBAAiB,CAAC,YAAY;IACjE,GAAC,kBAAkB,CAAC,gBAAgB,IAAG,iBAAiB,CAAC,gBAAgB;IAEzE,kCAAkC;IAClC,GAAC,iBAAiB,CAAC,eAAe,IAAG,iBAAiB,CAAC,eAAe;IACtE,GAAC,iBAAiB,CAAC,oBAAoB,IAAG,iBAAiB,CAAC,oBAAoB;IAChF,GAAC,iBAAiB,CAAC,eAAe,IAAG,iBAAiB,CAAC,eAAe;IACtE,GAAC,iBAAiB,CAAC,uBAAuB,IAAG,iBAAiB,CAAC,uBAAuB;IACtF,GAAC,iBAAiB,CAAC,mBAAmB,IAAG,iBAAiB,CAAC,mBAAmB;IAC9E,GAAC,iBAAiB,CAAC,cAAc,IAAG,iBAAiB,CAAC,cAAc;QACtE;AAeW,QAAA,sBAAsB,GAAmE;IAClG,KAAK,EAAE,OAAO;IACd,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,SAAS;IAClB,UAAU,EAAE,kBAAkB;IAC9B,YAAY,EAAE,cAAc;IAC5B,QAAQ,EAAE,UAAU;CACvB,CAAC;AAEW,QAAA,cAAc,GAA2D;IAClF,KAAK,EAAE,wBAAwB;IAC/B,SAAS,EAAE,4BAA4B;IACvC,KAAK,EAAE,wBAAwB;IAC/B,MAAM,EAAE,yBAAyB;IACjC,OAAO,EAAE,0BAA0B;IACnC,UAAU,EAAE,6BAA6B;IACzC,YAAY,EAAE,+BAA+B;IAC7C,QAAQ,EAAE,2BAA2B;CACxC,CAAC;AAEW,QAAA,mCAAmC,GAA6D;IACzG,SAAS,EAAE,sBAAc,CAAC,KAAK;IAC/B,aAAa,EAAE,sBAAc,CAAC,SAAS;IACvC,WAAW,EAAE,sBAAc,CAAC,KAAK;IACjC,UAAU,EAAE,sBAAc,CAAC,MAAM;IACjC,WAAW,EAAE,sBAAc,CAAC,OAAO;IACnC,oBAAoB,EAAE,sBAAc,CAAC,UAAU;IAC/C,gBAAgB,EAAE,sBAAc,CAAC,YAAY;IAC7C,YAAY,EAAE,sBAAc,CAAC,QAAQ;CACxC,CAAC;AAEW,QAAA,2CAA2C,GAAqE;IACzH,SAAS,EAAE,8BAAsB,CAAC,KAAK;IACvC,aAAa,EAAE,8BAAsB,CAAC,SAAS;IAC/C,WAAW,EAAE,8BAAsB,CAAC,KAAK;IACzC,UAAU,EAAE,8BAAsB,CAAC,MAAM;IACzC,WAAW,EAAE,8BAAsB,CAAC,OAAO;IAC3C,oBAAoB,EAAE,8BAAsB,CAAC,UAAU;IACvD,gBAAgB,EAAE,8BAAsB,CAAC,YAAY;IACrD,YAAY,EAAE,8BAAsB,CAAC,QAAQ;CAChD,CAAC;AAEF,gCAAgC;AACnB,QAAA,aAAa,GAAG;IACzB,OAAO;IACP,QAAQ,EAAE,WAAW;IACrB,UAAU,EAAE,aAAa;IACzB,QAAQ,EAAE,WAAW;IACrB,cAAc,EAAE,kBAAkB;IAClC,OAAO,EAAE,UAAU;IACnB,aAAa,EAAE,iBAAiB;IAEhC,OAAO;IACP,UAAU,EAAE,aAAa;IACzB,WAAW,EAAE,aAAa;CAC7B,CAAC;AAEW,QAAA,kBAAkB,GAAG;IAC9B,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,QAAQ;CACnB,CAAA;AAEY,QAAA,mBAAmB,GAAG,WAAW,CAAC", "sourcesContent": ["import Contracts = require(\"./Contracts\")\r\n\r\nexport const DEFAULT_BREEZE_ENDPOINT = \"https://dc.services.visualstudio.com\";\r\nexport const DEFAULT_LIVEMETRICS_ENDPOINT = \"https://rt.services.visualstudio.com\";\r\nexport const DEFAULT_LIVEMETRICS_HOST = \"rt.services.visualstudio.com\";\r\n\r\nexport enum QuickPulseCounter {\r\n    // Memory\r\n    COMMITTED_BYTES= \"\\\\Memory\\\\Committed Bytes\",\r\n\r\n    // CPU\r\n    PROCESSOR_TIME= \"\\\\Processor(_Total)\\\\% Processor Time\",\r\n\r\n    // Request\r\n    REQUEST_RATE= \"\\\\ApplicationInsights\\\\Requests\\/Sec\",\r\n    REQUEST_FAILURE_RATE= \"\\\\ApplicationInsights\\\\Requests Failed\\/Sec\",\r\n    REQUEST_DURATION= \"\\\\ApplicationInsights\\\\Request Duration\",\r\n\r\n    // Dependency\r\n    DEPENDENCY_RATE= \"\\\\ApplicationInsights\\\\Dependency Calls\\/Sec\",\r\n    DEPENDENCY_FAILURE_RATE= \"\\\\ApplicationInsights\\\\Dependency Calls Failed\\/Sec\",\r\n    DEPENDENCY_DURATION= \"\\\\ApplicationInsights\\\\Dependency Call Duration\",\r\n\r\n    // Exception\r\n    EXCEPTION_RATE= \"\\\\ApplicationInsights\\\\Exceptions\\/Sec\"\r\n}\r\n\r\nexport enum PerformanceCounter {\r\n    // Memory\r\n    PRIVATE_BYTES= \"\\\\Process(??APP_WIN32_PROC??)\\\\Private Bytes\",\r\n    AVAILABLE_BYTES= \"\\\\Memory\\\\Available Bytes\",\r\n\r\n    // CPU\r\n    PROCESSOR_TIME= \"\\\\Processor(_Total)\\\\% Processor Time\",\r\n    PROCESS_TIME= \"\\\\Process(??APP_WIN32_PROC??)\\\\% Processor Time\",\r\n\r\n    // Requests\r\n    REQUEST_RATE= \"\\\\ASP.NET Applications(??APP_W3SVC_PROC??)\\\\Requests/Sec\",\r\n    REQUEST_DURATION= \"\\\\ASP.NET Applications(??APP_W3SVC_PROC??)\\\\Request Execution Time\"\r\n};\r\n\r\n/**\r\n * Map a PerformanceCounter/QuickPulseCounter to a QuickPulseCounter. If no mapping exists, mapping is *undefined*\r\n */\r\nexport const PerformanceToQuickPulseCounter: {[key: string]: QuickPulseCounter} = {\r\n    [PerformanceCounter.PROCESSOR_TIME]: QuickPulseCounter.PROCESSOR_TIME,\r\n    [PerformanceCounter.REQUEST_RATE]: QuickPulseCounter.REQUEST_RATE,\r\n    [PerformanceCounter.REQUEST_DURATION]: QuickPulseCounter.REQUEST_DURATION,\r\n\r\n    // Remap quick pulse only counters\r\n    [QuickPulseCounter.COMMITTED_BYTES]: QuickPulseCounter.COMMITTED_BYTES,\r\n    [QuickPulseCounter.REQUEST_FAILURE_RATE]: QuickPulseCounter.REQUEST_FAILURE_RATE,\r\n    [QuickPulseCounter.DEPENDENCY_RATE]: QuickPulseCounter.DEPENDENCY_RATE,\r\n    [QuickPulseCounter.DEPENDENCY_FAILURE_RATE]: QuickPulseCounter.DEPENDENCY_FAILURE_RATE,\r\n    [QuickPulseCounter.DEPENDENCY_DURATION]: QuickPulseCounter.DEPENDENCY_DURATION,\r\n    [QuickPulseCounter.EXCEPTION_RATE]: QuickPulseCounter.EXCEPTION_RATE\r\n};\r\n\r\n// Note: Explicitly define these types instead of using enum due to\r\n// potential 'export enum' issues with typescript < 2.0.\r\nexport type QuickPulseDocumentType = \"Event\" | \"Exception\" | \"Trace\" | \"Metric\" | \"Request\" | \"RemoteDependency\" | \"Availability\" | \"PageView\";\r\nexport type QuickPulseType =\r\n    | \"EventTelemetryDocument\"\r\n    | \"ExceptionTelemetryDocument\"\r\n    | \"TraceTelemetryDocument\"\r\n    | \"MetricTelemetryDocument\"\r\n    | \"RequestTelemetryDocument\"\r\n    | \"DependencyTelemetryDocument\"\r\n    | \"AvailabilityTelemetryDocument\"\r\n    | \"PageViewTelemetryDocument\";\r\n\r\nexport const QuickPulseDocumentType: {[key in Contracts.TelemetryTypeKeys]: QuickPulseDocumentType} = {\r\n    Event: \"Event\",\r\n    Exception: \"Exception\",\r\n    Trace: \"Trace\",\r\n    Metric: \"Metric\",\r\n    Request: \"Request\",\r\n    Dependency: \"RemoteDependency\",\r\n    Availability: \"Availability\",\r\n    PageView: \"PageView\",\r\n};\r\n\r\nexport const QuickPulseType: {[key in Contracts.TelemetryTypeKeys]: QuickPulseType} = {\r\n    Event: \"EventTelemetryDocument\",\r\n    Exception: \"ExceptionTelemetryDocument\",\r\n    Trace: \"TraceTelemetryDocument\",\r\n    Metric: \"MetricTelemetryDocument\",\r\n    Request: \"RequestTelemetryDocument\",\r\n    Dependency: \"DependencyTelemetryDocument\",\r\n    Availability: \"AvailabilityTelemetryDocument\",\r\n    PageView: \"PageViewTelemetryDocument\",\r\n};\r\n\r\nexport const TelemetryTypeStringToQuickPulseType: {[key in Contracts.TelemetryTypeValues]: QuickPulseType} = {\r\n    EventData: QuickPulseType.Event,\r\n    ExceptionData: QuickPulseType.Exception,\r\n    MessageData: QuickPulseType.Trace,\r\n    MetricData: QuickPulseType.Metric,\r\n    RequestData: QuickPulseType.Request,\r\n    RemoteDependencyData: QuickPulseType.Dependency,\r\n    AvailabilityData: QuickPulseType.Availability,\r\n    PageViewData: QuickPulseType.PageView\r\n};\r\n\r\nexport const TelemetryTypeStringToQuickPulseDocumentType: {[key in Contracts.TelemetryTypeValues]: QuickPulseDocumentType} = {\r\n    EventData: QuickPulseDocumentType.Event,\r\n    ExceptionData: QuickPulseDocumentType.Exception,\r\n    MessageData: QuickPulseDocumentType.Trace,\r\n    MetricData: QuickPulseDocumentType.Metric,\r\n    RequestData: QuickPulseDocumentType.Request,\r\n    RemoteDependencyData: QuickPulseDocumentType.Dependency,\r\n    AvailabilityData: QuickPulseDocumentType.Availability,\r\n    PageViewData: QuickPulseDocumentType.PageView\r\n};\r\n\r\n// OpenTelemetry Span Attributes\r\nexport const SpanAttribute = {\r\n    // HTTP\r\n    HttpHost: \"http.host\",\r\n    HttpMethod: \"http.method\",\r\n    HttpPort: \"http.port\",\r\n    HttpStatusCode: \"http.status_code\",\r\n    HttpUrl: \"http.url\",\r\n    HttpUserAgent: \"http.user_agent\",\r\n\r\n    // GRPC\r\n    GrpcMethod: \"grpc.method\",\r\n    GrpcService: \"rpc.service\", // rpc not grpc\r\n};\r\n\r\nexport const DependencyTypeName = {\r\n    Grpc: \"GRPC\",\r\n    Http: \"HTTP\",\r\n    InProc: \"InProc\",\r\n}\r\n\r\nexport const HeartBeatMetricName = \"HeartBeat\";"]}