{"version": 3, "file": "ExceptionDocumentQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/ExceptionDocumentQuickPulse.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentQuickPulse } from \"./DocumentQuickPulse\";\r\n\r\nexport interface ExceptionDocumentQuickPulse extends DocumentQuickPulse {\r\n    Exception: string;\r\n    ExceptionMessage: string;\r\n    ExceptionType: string;\r\n}\r\n"]}