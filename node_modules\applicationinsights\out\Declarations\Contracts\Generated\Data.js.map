{"version": 3, "file": "Data.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/Data.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,6BAAgC;AAChC,YAAY,CAAC;AAET;;GAEG;AACH;IAA4B,wBAAI;IAa5B;eAEI,iBAAO;IAEX,CAAC;IACL,WAAC;AAAD,CAAC,AAlBD,CAA4B,IAAI,GAkB/B;AACL,iBAAS,IAAI,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Base = require('./Base');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Data struct to contain both B and C sections.\r\n     */\r\n    class Data<TDomain> extends Base\r\n    {\r\n        \r\n        /**\r\n         * Name of item (B section) if any. If telemetry data is derived straight from this, this should be null.\r\n         */\r\n        public baseType: string;\r\n        \r\n        /**\r\n         * Container for data item (B section).\r\n         */\r\n        public baseData: TDomain;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n        }\r\n    }\r\nexport = Data;\r\n"]}