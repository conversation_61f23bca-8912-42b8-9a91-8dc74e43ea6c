{"version": 3, "file": "TelemetryType.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/TelemetryType.ts"], "names": [], "mappings": ";;AAWA;;;GAGG;AACH,iCAAwC,IAAmB;IACvD,MAAM,CAAA,CAAC,IAAI,CAAC,CAAC,CAAC;QACV,KAAK,aAAa,CAAC,KAAK;YACpB,MAAM,CAAC,WAAW,CAAC;QACvB,KAAK,aAAa,CAAC,SAAS;YACxB,MAAM,CAAC,eAAe,CAAC;QAC3B,KAAK,aAAa,CAAC,KAAK;YACpB,MAAM,CAAC,aAAa,CAAC;QACzB,KAAK,aAAa,CAAC,MAAM;YACrB,MAAM,CAAC,YAAY,CAAC;QACxB,KAAK,aAAa,CAAC,OAAO;YACtB,MAAM,CAAC,aAAa,CAAC;QACzB,KAAK,aAAa,CAAC,UAAU;YACzB,MAAM,CAAC,sBAAsB,CAAC;QAClC,KAAK,aAAa,CAAC,YAAY;YAC3B,MAAM,CAAC,kBAAkB,CAAC;QAC9B,KAAK,aAAa,CAAC,QAAQ;YACvB,MAAM,CAAC,cAAc,CAAC;IAC9B,CAAC;IACD,MAAM,CAAC,SAAS,CAAC;AACrB,CAAC;AApBD,0DAoBC;AAED;;;GAGG;AACH,iCAAwC,QAA6B;IACjE,MAAM,CAAA,CAAC,QAAQ,CAAC,CAAC,CAAC;QACd,KAAK,WAAW;YACZ,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC;QAC/B,KAAK,eAAe;YAChB,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;QACnC,KAAK,aAAa;YACd,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC;QAC/B,KAAK,YAAY;YACb,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;QAChC,KAAK,aAAa;YACd,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC;QACjC,KAAK,sBAAsB;YACvB,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;QACpC,KAAK,kBAAkB;YACnB,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC;QACtC,KAAK,cAAc;YACf,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;IACtC,CAAC;IACD,MAAM,CAAC,SAAS,CAAC;AACrB,CAAC;AApBD,0DAoBC;AAEY,QAAA,mBAAmB,GAAyC;IACrE,KAAK,EAAE,WAAW;IAClB,SAAS,EAAE,eAAe;IAC1B,KAAK,EAAE,aAAa;IACpB,MAAM,EAAE,YAAY;IACpB,OAAO,EAAE,aAAa;IACtB,UAAU,EAAE,sBAAsB;IAClC,YAAY,EAAE,kBAAkB;IAChC,QAAQ,EAAE,cAAc;CAC3B,CAAA;AAED;;GAEG;AACH,IAAY,aASX;AATD,WAAY,aAAa;IACrB,mDAAK,CAAA;IACL,2DAAS,CAAA;IACT,mDAAK,CAAA;IACL,qDAAM,CAAA;IACN,uDAAO,CAAA;IACP,6DAAU,CAAA;IACV,iEAAY,CAAA;IACZ,yDAAQ,CAAA;AACZ,CAAC,EATW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QASxB", "sourcesContent": ["export type TelemetryTypeKeys = \"Event\" | \"Exception\" | \"Trace\" | \"Metric\" | \"Request\" | \"Dependency\" | \"Availability\" | \"PageView\";\r\nexport type TelemetryTypeValues =\r\n    | \"EventData\"\r\n    | \"ExceptionData\"\r\n    | \"MessageData\"\r\n    | \"MetricData\"\r\n    | \"RequestData\"\r\n    | \"RemoteDependencyData\"\r\n    | \"AvailabilityData\"\r\n    | \"PageViewData\";\r\n\r\n/**\r\n * Converts the user-friendly enumeration TelemetryType to the underlying schema baseType value\r\n * @param type Type to convert to BaseData string\r\n */\r\nexport function telemetryTypeToBaseType(type: TelemetryType): TelemetryTypeValues {\r\n    switch(type) {\r\n        case TelemetryType.Event:\r\n            return \"EventData\";\r\n        case TelemetryType.Exception:\r\n            return \"ExceptionData\";\r\n        case TelemetryType.Trace:\r\n            return \"MessageData\";\r\n        case TelemetryType.Metric:\r\n            return \"MetricData\";\r\n        case TelemetryType.Request:\r\n            return \"RequestData\";\r\n        case TelemetryType.Dependency:\r\n            return \"RemoteDependencyData\";\r\n        case TelemetryType.Availability:\r\n            return \"AvailabilityData\";\r\n        case TelemetryType.PageView:\r\n            return \"PageViewData\";\r\n    }\r\n    return undefined;\r\n}\r\n\r\n/**\r\n * Converts the schema baseType value to the user-friendly enumeration TelemetryType\r\n * @param baseType BaseData string to convert to TelemetryType\r\n */\r\nexport function baseTypeToTelemetryType(baseType: TelemetryTypeValues): TelemetryType {\r\n    switch(baseType) {\r\n        case \"EventData\":\r\n            return TelemetryType.Event;\r\n        case \"ExceptionData\":\r\n            return TelemetryType.Exception;\r\n        case \"MessageData\":\r\n            return TelemetryType.Trace;\r\n        case \"MetricData\":\r\n            return TelemetryType.Metric;\r\n        case \"RequestData\":\r\n            return TelemetryType.Request;\r\n        case \"RemoteDependencyData\":\r\n            return TelemetryType.Dependency;\r\n        case \"AvailabilityData\":\r\n            return TelemetryType.Availability;\r\n        case \"PageViewData\":\r\n            return TelemetryType.PageView;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nexport const TelemetryTypeString: {[key: string]: TelemetryTypeValues} = {\r\n    Event: \"EventData\",\r\n    Exception: \"ExceptionData\",\r\n    Trace: \"MessageData\",\r\n    Metric: \"MetricData\",\r\n    Request: \"RequestData\",\r\n    Dependency: \"RemoteDependencyData\",\r\n    Availability: \"AvailabilityData\",\r\n    PageView: \"PageViewData\",\r\n}\r\n\r\n/**\r\n * Telemetry types supported by this SDK\r\n */\r\nexport enum TelemetryType {\r\n    Event,\r\n    Exception,\r\n    Trace,\r\n    Metric,\r\n    Request,\r\n    Dependency,\r\n    Availability,\r\n    PageView\r\n}\r\n\r\nexport interface Identified {\r\n    id?: string;\r\n}\r\n"]}