import { Domain } from "./Generated";
export declare class RemoteDependencyDataConstants {
    static TYPE_HTTP: string;
    static TYPE_AI: string;
}
export interface ISupportProperties extends Domain {
    properties: any;
}
export declare function domainSupportsProperties(domain: Domain): domain is ISupportProperties;
/**
 * Subset of Connection String fields which this SDK can parse. Lower-typecased to
 * allow for case-insensitivity across field names
 * @type ConnectionStringKey
 */
export interface ConnectionString {
    authorization?: string;
    instrumentationkey?: string;
    ingestionendpoint?: string;
    liveendpoint?: string;
    location?: string;
    endpointsuffix?: string;
}
export declare type ConnectionStringKey = "authorization" | "instrumentationkey" | "ingestionendpoint" | "liveendpoint" | "location" | "endpointsuffix";
