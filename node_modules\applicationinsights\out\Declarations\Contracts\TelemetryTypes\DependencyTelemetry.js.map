{"version": 3, "file": "DependencyTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/DependencyTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry about the call to remote component\r\n */\r\nexport interface DependencyTelemetry extends Telemetry {\r\n    /**\r\n     * Type name of the telemetry, such as HTTP of SQL\r\n     */\r\n    dependencyTypeName: string;\r\n\r\n    /**\r\n     * Remote component general target information\r\n     * If left empty, this will be prepopulated with an extracted hostname from the data field, if it is a url.\r\n     * This prepopulation happens when calling `trackDependency`. Use `track` directly to avoid this behavior.\r\n     */\r\n    target?: string;\r\n\r\n    /**\r\n     * Remote call name\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * Remote call data. This is the most detailed information about the call, such as full URL or SQL statement\r\n     */\r\n    data: string;\r\n\r\n    /**\r\n     * Remote call duration in ms\r\n     */\r\n    duration: number;\r\n\r\n    /**\r\n     * Result code returned form the remote component. This is domain specific and can be HTTP status code or SQL result code\r\n     */\r\n    resultCode: string | number;\r\n\r\n    /**\r\n     * True if remote call was successful, false otherwise\r\n     */\r\n    success: boolean;\r\n}"]}