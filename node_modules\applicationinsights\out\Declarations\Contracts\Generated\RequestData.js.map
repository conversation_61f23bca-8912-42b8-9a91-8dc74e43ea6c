{"version": 3, "file": "RequestData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/RequestData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AACpC,YAAY,CAAC;AAET;;GAEG;AACH;IAA0B,+BAAM;IAqD5B;QAAA,YAEI,iBAAO,SAKV;QAHG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,kBAAC;AAAD,CAAC,AA7DD,CAA0B,MAAM,GA6D/B;AACL,iBAAS,WAAW,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * An instance of Request represents completion of an external request to the application to do work and contains a summary of that request execution and the results.\r\n     */\r\n    class RequestData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Identifier of a request call instance. Used for correlation between request and other telemetry items.\r\n         */\r\n        public id: string;\r\n        \r\n        /**\r\n         * Source of the request. Examples are the instrumentation key of the caller or the ip address of the caller.\r\n         */\r\n        public source: string;\r\n        \r\n        /**\r\n         * Name of the request. Represents code path taken to process request. Low cardinality value to allow better grouping of requests. For HTTP requests it represents the HTTP method and URL path template like 'GET /values/{id}'.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Request duration in format: DD.HH:MM:SS.MMMMMM. Must be less than 1000 days.\r\n         */\r\n        public duration: string;\r\n        \r\n        /**\r\n         * Result of a request execution. HTTP status code for HTTP requests.\r\n         */\r\n        public responseCode: string;\r\n        \r\n        /**\r\n         * Indication of successfull or unsuccessfull call.\r\n         */\r\n        public success: boolean;\r\n        \r\n        /**\r\n         * Request URL with all query string parameters.\r\n         */\r\n        public url: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = RequestData;\r\n"]}