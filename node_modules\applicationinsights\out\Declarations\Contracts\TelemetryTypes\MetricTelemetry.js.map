{"version": 3, "file": "MetricTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/MetricTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry encapsulating a custom metric, i.e. aggregated numeric values describing value, count, frequency and distribution of\r\n * of a particular indicator.\r\n */\r\nexport interface MetricTelemetry extends Telemetry {\r\n    /**\r\n     * A string that identifies the metric.\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The value of the metric\r\n     */\r\n    value: number;\r\n\r\n    /**\r\n     * The number of samples used to get this value\r\n     */\r\n    count?: number;\r\n\r\n    /**\r\n     * The min sample for this set\r\n     */\r\n    min?: number;\r\n\r\n    /**\r\n     * The max sample for this set\r\n     */\r\n    max?: number;\r\n\r\n    /**\r\n     * The standard deviation of the set\r\n     */\r\n    stdDev?: number;\r\n}"]}