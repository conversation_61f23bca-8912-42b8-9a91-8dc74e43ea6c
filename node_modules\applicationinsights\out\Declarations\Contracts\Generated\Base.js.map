{"version": 3, "file": "Base.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/Base.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AAET;;GAEG;AACH;IAQI;IAEA,CAAC;IACL,WAAC;AAAD,CAAC,AAXD,IAWC;AACL,iBAAS,IAAI,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Data struct to contain only C section with custom fields.\r\n     */\r\n    class Base\r\n    {\r\n        \r\n        /**\r\n         * Name of item (B section) if any. If telemetry data is derived straight from this, this should be null.\r\n         */\r\n        public baseType: string;\r\n        \r\n        constructor()\r\n        {\r\n        }\r\n    }\r\nexport = Base;\r\n"]}