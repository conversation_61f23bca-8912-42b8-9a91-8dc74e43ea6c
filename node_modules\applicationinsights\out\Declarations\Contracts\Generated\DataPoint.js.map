{"version": 3, "file": "DataPoint.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/DataPoint.ts"], "names": [], "mappings": ";AAAA,8BAA8B;AAC9B,+CAAkD;AAClD,YAAY,CAAC;AAET;;GAEG;AACH;IAsCI;QAEI,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IACL,gBAAC;AAAD,CAAC,AA1CD,IA0CC;AACL,iBAAS,SAAS,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport DataPointType = require('./DataPointType');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Metric data single measurement.\r\n     */\r\n    class DataPoint\r\n    {\r\n        \r\n        /**\r\n         * Name of the metric.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Metric type. Single measurement or the aggregated value.\r\n         */\r\n        public kind: DataPointType;\r\n        \r\n        /**\r\n         * Single value for measurement. Sum of individual measurements for the aggregation.\r\n         */\r\n        public value: number;\r\n        \r\n        /**\r\n         * Metric weight of the aggregated metric. Should not be set for a measurement.\r\n         */\r\n        public count: number;\r\n        \r\n        /**\r\n         * Minimum value of the aggregated metric. Should not be set for a measurement.\r\n         */\r\n        public min: number;\r\n        \r\n        /**\r\n         * Maximum value of the aggregated metric. Should not be set for a measurement.\r\n         */\r\n        public max: number;\r\n        \r\n        /**\r\n         * Standard deviation of the aggregated metric. Should not be set for a measurement.\r\n         */\r\n        public stdDev: number;\r\n        \r\n        constructor()\r\n        {\r\n            this.kind = DataPointType.Measurement;\r\n        }\r\n    }\r\nexport = DataPoint;\r\n"]}