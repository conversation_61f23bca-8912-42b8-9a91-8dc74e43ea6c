{"version": 3, "file": "Domain.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/Domain.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AAET;;GAEG;AACH;IAGI;IAEA,CAAC;IACL,aAAC;AAAD,CAAC,AAND,IAMC;AACL,iBAAS,MAAM,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\n    \r\n    /**\r\n     * The abstract common base of all domains.\r\n     */\r\n    class Domain\r\n    {\r\n        \r\n        constructor()\r\n        {\r\n        }\r\n    }\r\nexport = Domain;\r\n"]}