{"version": 3, "file": "MessageDocumentQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/MessageDocumentQuickPulse.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentQuickPulse } from \"./DocumentQuickPulse\";\r\n\r\nexport interface MessageDocumentQuickPulse extends DocumentQuickPulse {\r\n    Message: string;\r\n    SeverityLevel: string;\r\n}\r\n"]}