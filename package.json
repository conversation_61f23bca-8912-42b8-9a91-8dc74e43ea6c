{"name": "xlpane", "version": "1.0.0", "description": "A modern Excel add-in for viewing and editing cell content in a side pane with sliding drawer functionality", "main": "taskpane.html", "scripts": {"start": "npx http-server -p 3000 -c-1 --cors --ssl --cert ./certs/cert.pem --key ./certs/key.pem", "dev": "npx http-server -p 3000 -c-1 --cors --ssl --cert ./certs/cert.pem --key ./certs/key.pem -o", "build": "echo 'Build completed - files are ready for deployment'", "validate": "npx office-addin-manifest validate manifest.xml", "sideload": "npx office-addin-dev-settings sideload manifest.xml", "unsideload": "npx office-addin-dev-settings remove manifest.xml", "lint": "npx eslint taskpane.js", "format": "npx prettier --write *.html *.css *.js"}, "keywords": ["excel", "office-addin", "taskpane", "cell-editor", "side-panel", "modern-ui"], "author": "XLPane Team", "license": "MIT", "devDependencies": {"http-server": "^14.1.1", "office-addin-manifest": "^1.12.3", "office-addin-dev-settings": "^2.1.5", "eslint": "^8.57.0", "prettier": "^3.2.5"}, "repository": {"type": "git", "url": "https://github.com/xlpane/xlpane.git"}, "bugs": {"url": "https://github.com/xlpane/xlpane/issues"}, "homepage": "https://github.com/xlpane/xlpane#readme", "engines": {"node": ">=14.0.0"}, "browserslist": ["last 2 versions", "not dead", "not < 2%"]}