{"version": 3, "file": "PageViewData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/PageViewData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,uCAA0C;AAC1C,YAAY,CAAC;AAET;;GAEG;AACH;IAA2B,gCAAS;IAiChC;QAAA,YAEI,iBAAO,SAKV;QAHG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,mBAAC;AAAD,CAAC,AAzCD,CAA2B,SAAS,GAyCnC;AACL,iBAAS,YAAY,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport EventData = require('./EventData');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * An instance of PageView represents a generic action on a page like a button click. It is also the base type for PageView.\r\n     */\r\n    class PageViewData extends EventData\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Request URL with all query string parameters\r\n         */\r\n        public url: string;\r\n        \r\n        /**\r\n         * Event name. Keep it low cardinality to allow proper grouping and useful metrics.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Request duration in format: DD.HH:MM:SS.MMMMMM. For a page view (PageViewData), this is the duration. For a page view with performance information (PageViewPerfData), this is the page load time. Must be less than 1000 days.\r\n         */\r\n        public duration: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = PageViewData;\r\n"]}