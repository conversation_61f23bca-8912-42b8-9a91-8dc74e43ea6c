{"version": 3, "file": "StackFrame.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/StackFrame.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AAET;;GAEG;AACH;IA4BI;IAEA,CAAC;IACL,iBAAC;AAAD,CAAC,AA/BD,IA+BC;AACL,iBAAS,UAAU,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Stack frame information.\r\n     */\r\n    class StackFrame\r\n    {\r\n        \r\n        /**\r\n         * Level in the call stack. For the long stacks SDK may not report every function in a call stack.\r\n         */\r\n        public level: number;\r\n        \r\n        /**\r\n         * Method name.\r\n         */\r\n        public method: string;\r\n        \r\n        /**\r\n         * Name of the assembly (dll, jar, etc.) containing this function.\r\n         */\r\n        public assembly: string;\r\n        \r\n        /**\r\n         * File name or URL of the method implementation.\r\n         */\r\n        public fileName: string;\r\n        \r\n        /**\r\n         * Line number of the code implementation.\r\n         */\r\n        public line: number;\r\n        \r\n        constructor()\r\n        {\r\n        }\r\n    }\r\nexport = StackFrame;\r\n"]}