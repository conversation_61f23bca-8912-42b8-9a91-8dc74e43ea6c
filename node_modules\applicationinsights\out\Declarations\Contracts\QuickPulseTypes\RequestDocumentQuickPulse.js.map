{"version": 3, "file": "RequestDocumentQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/RequestDocumentQuickPulse.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentQuickPulse } from \"./DocumentQuickPulse\";\r\n\r\nexport interface RequestDocumentQuickPulse extends DocumentQuickPulse {\r\n    Name: string;\r\n    Success?: boolean;\r\n    Duration: string;\r\n    ResponseCode: string,\r\n    OperationName: string;\r\n}\r\n"]}