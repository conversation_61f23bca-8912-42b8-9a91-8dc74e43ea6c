{"version": 3, "file": "Envelope.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/Envelope.ts"], "names": [], "mappings": "AAEA,YAAY,CAAC;AAET;;GAEG;AACH;IA2CI;QAEI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACnB,CAAC;IACL,eAAC;AAAD,CAAC,AAjDD,IAiDC;AACL,iBAAS,QAAQ,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Base = require('./Base');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * System variables for a telemetry item.\r\n     */\r\n    class Envelope\r\n    {\r\n        \r\n        /**\r\n         * Envelope version. For internal use only. By assigning this the default, it will not be serialized within the payload unless changed to a value other than #1.\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Type name of telemetry data item.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Event date time when telemetry item was created. This is the wall clock time on the client when the event was generated. There is no guarantee that the client's time is accurate. This field must be formatted in UTC ISO 8601 format, with a trailing 'Z' character, as described publicly on https://en.wikipedia.org/wiki/ISO_8601#UTC. Note: the number of decimal seconds digits provided are variable (and unspecified). Consumers should handle this, i.e. managed code consumers should not use format 'O' for parsing as it specifies a fixed length. Example: 2009-06-15T13:45:30.0000000Z.\r\n         */\r\n        public time: string;\r\n        \r\n        /**\r\n         * Sampling rate used in application. This telemetry item represents 1 / sampleRate actual telemetry items.\r\n         */\r\n        public sampleRate: number;\r\n        \r\n        /**\r\n         * Sequence field used to track absolute order of uploaded events.\r\n         */\r\n        public seq: string;\r\n        \r\n        /**\r\n         * The application's instrumentation key. The key is typically represented as a GUID, but there are cases when it is not a guid. No code should rely on iKey being a GUID. Instrumentation key is case insensitive.\r\n         */\r\n        public iKey: string;\r\n        \r\n        /**\r\n         * Key/value collection of context properties. See ContextTagKeys for information on available properties.\r\n         */\r\n        public tags: any;\r\n        \r\n        /**\r\n         * Telemetry data item.\r\n         */\r\n        public data: Base;\r\n        \r\n        constructor()\r\n        {\r\n            this.ver = 1;\r\n            this.sampleRate = 100.0;\r\n            this.tags = {};\r\n        }\r\n    }\r\nexport = Envelope;\r\n"]}