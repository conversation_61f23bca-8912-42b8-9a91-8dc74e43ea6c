{"version": 3, "file": "DependencyDocumentQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/DependencyDocumentQuickPulse.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentQuickPulse } from \"./DocumentQuickPulse\";\r\n\r\nexport interface DependencyDocumentQuickPulse extends DocumentQuickPulse {\r\n    Name: string;\r\n    Target: string;\r\n    Success?: boolean;\r\n    Duration: string;\r\n    ResultCode: string,\r\n    CommandName: string;\r\n    DependencyTypeName: string;\r\n    OperationName: string;\r\n}\r\n"]}