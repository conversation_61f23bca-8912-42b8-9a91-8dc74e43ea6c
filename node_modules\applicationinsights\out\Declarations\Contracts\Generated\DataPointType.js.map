{"version": 3, "file": "DataPointType.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/DataPointType.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AAET;;GAEG;AACH,IAAK,aAIJ;AAJD,WAAK,aAAa;IAEd,+DAAe,CAAA;IACf,+DAAe,CAAA;AACnB,CAAC,EAJI,aAAa,KAAb,aAAa,QAIjB;AACL,iBAAS,aAAa,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Type of the metric data measurement.\r\n     */\r\n    enum DataPointType\r\n    {\r\n        Measurement = 0,\r\n        Aggregation = 1,\r\n    }\r\nexport = DataPointType;\r\n"]}