{"version": 3, "file": "Constants.js", "sourceRoot": "", "sources": ["../../../Declarations/Contracts/Constants.ts"], "names": [], "mappings": ";;AAAA,yCAAyI;AAEzI;IAAA;IAGA,CAAC;IAFiB,uCAAS,GAAU,MAAM,CAAC;IAC1B,qCAAO,GAAU,0BAA0B,CAAC;IAC9D,oCAAC;CAAA,AAHD,IAGC;AAHY,sEAA6B;AAS1C,kCAAyC,MAAc;IACnD,MAAM,CAAC,YAAY,IAAI,MAAM;QACzB,MAAM,YAAY,qBAAS;QAC3B,MAAM,YAAY,yBAAa;QAC/B,MAAM,YAAY,uBAAW;QAC7B,MAAM,YAAY,sBAAU;QAC5B,MAAM,YAAY,wBAAY;QAC9B,MAAM,YAAY,gCAAoB;QACtC,MAAM,YAAY,uBAAW,CAAC;AACtC,CAAC;AATD,4DASC", "sourcesContent": ["import { Domain, EventData, ExceptionData, MessageData, MetricData, PageViewData, RemoteDependencyData, RequestData } from \"./Generated\";\r\n\r\nexport class RemoteDependencyDataConstants {\r\n    public static TYPE_HTTP:string = \"Http\";\r\n    public static TYPE_AI:string = \"Http (tracked component)\";\r\n}\r\n\r\nexport interface ISupportProperties extends Domain {\r\n    properties: any;\r\n}\r\n\r\nexport function domainSupportsProperties(domain: Domain): domain is ISupportProperties {\r\n    return \"properties\" in domain || // Do extra typechecks in case the type supports it but properties is null/undefined\r\n        domain instanceof EventData ||\r\n        domain instanceof ExceptionData ||\r\n        domain instanceof MessageData ||\r\n        domain instanceof MetricData ||\r\n        domain instanceof PageViewData ||\r\n        domain instanceof RemoteDependencyData ||\r\n        domain instanceof RequestData;\r\n}\r\n\r\n/**\r\n * Subset of Connection String fields which this SDK can parse. Lower-typecased to\r\n * allow for case-insensitivity across field names\r\n * @type ConnectionStringKey\r\n */\r\nexport interface ConnectionString {\r\n    authorization?: string;\r\n    instrumentationkey?: string;\r\n    ingestionendpoint?: string;\r\n    liveendpoint?: string;\r\n    location?: string;\r\n    endpointsuffix?: string;\r\n\r\n    // Note: this is a node types backcompat equivalent to\r\n    // type ConnectionString = { [key in ConnectionStringKey]?: string }\r\n}\r\n\r\nexport type ConnectionStringKey = \"authorization\" | \"instrumentationkey\" | \"ingestionendpoint\" | \"liveendpoint\" | \"location\" | \"endpointsuffix\";\r\n"]}