{"version": 3, "file": "RemoteDependencyData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/RemoteDependencyData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AACpC,YAAY,CAAC;AAET;;GAEG;AACH;IAAmC,wCAAM;IA0DrC;QAAA,YAEI,iBAAO,SAMV;QAJG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,2BAAC;AAAD,CAAC,AAnED,CAAmC,MAAM,GAmExC;AACL,iBAAS,oBAAoB,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * An instance of Remote Dependency represents an interaction of the monitored component with a remote component/service like SQL or an HTTP endpoint.\r\n     */\r\n    class RemoteDependencyData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Name of the command initiated with this dependency call. Low cardinality value. Examples are stored procedure name and URL path template.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Identifier of a dependency call instance. Used for correlation with the request telemetry item corresponding to this dependency call.\r\n         */\r\n        public id: string;\r\n        \r\n        /**\r\n         * Result code of a dependency call. Examples are SQL error code and HTTP status code.\r\n         */\r\n        public resultCode: string;\r\n        \r\n        /**\r\n         * Request duration in format: DD.HH:MM:SS.MMMMMM. Must be less than 1000 days.\r\n         */\r\n        public duration: string;\r\n        \r\n        /**\r\n         * Indication of successfull or unsuccessfull call.\r\n         */\r\n        public success: boolean;\r\n        \r\n        /**\r\n         * Command initiated by this dependency call. Examples are SQL statement and HTTP URL's with all query parameters.\r\n         */\r\n        public data: string;\r\n        \r\n        /**\r\n         * Target site of a dependency call. Examples are server name, host address.\r\n         */\r\n        public target: string;\r\n        \r\n        /**\r\n         * Dependency type name. Very low cardinality value for logical grouping of dependencies and interpretation of other fields like commandName and resultCode. Examples are SQL, Azure table, and HTTP.\r\n         */\r\n        public type: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.success = true;\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = RemoteDependencyData;\r\n"]}