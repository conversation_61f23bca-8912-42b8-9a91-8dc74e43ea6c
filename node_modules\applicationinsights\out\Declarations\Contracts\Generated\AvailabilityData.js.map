{"version": 3, "file": "AvailabilityData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/AvailabilityData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AACpC,YAAY,CAAC;AAET;;GAEG;AACH;IAA+B,oCAAM;IAgDjC;QAAA,YAEI,iBAAO,SAKV;QAHG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,uBAAC;AAAD,CAAC,AAxDD,CAA+B,MAAM,GAwDpC;AACL,iBAAS,gBAAgB,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Instances of AvailabilityData represent the result of executing an availability test.\r\n     */\r\n    class AvailabilityData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Identifier of a test run. Use it to correlate steps of test run and telemetry generated by the service.\r\n         */\r\n        public id: string;\r\n        \r\n        /**\r\n         * Name of the test that these availability results represent.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Duration in format: DD.HH:MM:SS.MMMMMM. Must be less than 1000 days.\r\n         */\r\n        public duration: string;\r\n        \r\n        /**\r\n         * Success flag.\r\n         */\r\n        public success: boolean;\r\n        \r\n        /**\r\n         * Name of the location where the test was run from.\r\n         */\r\n        public runLocation: string;\r\n        \r\n        /**\r\n         * Diagnostic message for the result.\r\n         */\r\n        public message: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = AvailabilityData;\r\n"]}