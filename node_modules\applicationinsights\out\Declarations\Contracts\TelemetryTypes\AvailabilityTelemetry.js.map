{"version": 3, "file": "AvailabilityTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/AvailabilityTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry type used for availability web test results.\r\n */\r\nexport interface AvailabilityTelemetry extends Telemetry {\r\n\r\n        /**\r\n         * Identifier of a test run. Use it to correlate steps of test run and telemetry generated by the service.\r\n         */\r\n        id: string;\r\n\r\n        /**\r\n         * Name of the test that these availability results represent.\r\n         */\r\n        name: string;\r\n\r\n        /**\r\n         * Request duration in ms\r\n         */\r\n        duration: number;\r\n\r\n        /**\r\n         * Success flag.\r\n         */\r\n        success: boolean;\r\n\r\n        /**\r\n         * Name of the location where the test was run from.\r\n         */\r\n        runLocation: string;\r\n\r\n        /**\r\n         * Diagnostic message for the result.\r\n         */\r\n        message: string;\r\n\r\n        /**\r\n         * Metrics associated with this event, displayed in Metrics Explorer on the portal.\r\n         */\r\n        measurements?: { [key: string]: number; };\r\n}"]}