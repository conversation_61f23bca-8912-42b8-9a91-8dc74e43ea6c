{"version": 3, "file": "EventTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/EventTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry about the custom event of interest, such application workflow event, business logic event (purchase) and anything that\r\n * you would like to track and aggregate by count. Event can contain measurements such as purchase amount associated with purchase event\r\n */\r\nexport interface EventTelemetry extends Telemetry\r\n{\r\n    /**\r\n     * Name of the event\r\n     */\r\n     name: string;\r\n     \r\n    /**\r\n     * Metrics associated with this event, displayed in Metrics Explorer on the portal.\r\n     */\r\n    measurements?: { [key: string]: number; };\r\n}"]}