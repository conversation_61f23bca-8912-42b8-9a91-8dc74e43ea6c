{"version": 3, "file": "DocumentQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/DocumentQuickPulse.ts"], "names": [], "mappings": ";;AAeC,CAAC", "sourcesContent": ["export interface DocumentQuickPulse {\r\n    __type: string;\r\n\r\n    DocumentType: string;\r\n\r\n    Version: string;\r\n\r\n    OperationId: string;\r\n\r\n    Properties: IDocumentProperty[];\r\n}\r\n\r\nexport interface IDocumentProperty {\r\n    key: string;\r\n    value: string;\r\n};"]}