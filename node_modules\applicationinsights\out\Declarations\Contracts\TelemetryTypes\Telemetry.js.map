{"version": 3, "file": "Telemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/Telemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Base telemetry interface encapsulating coming properties\r\n */\r\nexport interface Telemetry {\r\n    /**\r\n     * Telemetry time stamp. When it is not specified, current timestamp will be used.\r\n     */\r\n    time?: Date;\r\n    /**\r\n     * Additional data used to filter events and metrics in the portal. Defaults to empty.\r\n     */\r\n    properties?: { [key: string]: any; };\r\n    /**\r\n     * An event-specific context that will be passed to telemetry processors handling this event before it is sent. For a context spanning your entire operation, consider appInsights.getCorrelationContext\r\n     */\r\n    contextObjects?: { [name: string]: any; };\r\n    /**\r\n     * The context tags to use for this telemetry which overwrite default context values\r\n     */\r\n    tagOverrides?: { [key: string]: string; };\r\n}\r\n"]}