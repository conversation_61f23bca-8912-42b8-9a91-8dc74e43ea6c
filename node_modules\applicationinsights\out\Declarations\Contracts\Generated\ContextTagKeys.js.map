{"version": 3, "file": "ContextTagKeys.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/ContextTagKeys.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AACb;IA8HI;QACI,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,qBAAqB,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,CAAC;QACjD,IAAI,CAAC,wBAAwB,GAAG,8BAA8B,CAAC;QAC/D,IAAI,CAAC,0BAA0B,GAAG,gCAAgC,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,oBAAoB,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,CAAC;QACjD,IAAI,CAAC,kBAAkB,GAAG,wBAAwB,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,0BAA0B,CAAC;QACvD,IAAI,CAAC,gBAAgB,GAAG,sBAAsB,CAAC;IACnD,CAAC;IACL,qBAAC;AAAD,CAAC,AAvJD,IAuJC;AACD,iBAAS,cAAc,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\nclass ContextTagKeys {\r\n\r\n    /**\r\n     * Application version. Information in the application context fields is always about the application that is sending the telemetry.\r\n     */\r\n    public applicationVersion: string;\r\n\r\n    /**\r\n     * Unique client device id. Computer name in most cases.\r\n     */\r\n    public deviceId: string;\r\n\r\n    /**\r\n     * Device locale using <language>-<REGION> pattern, following RFC 5646. Example 'en-US'.\r\n     */\r\n    public deviceLocale: string;\r\n\r\n    /**\r\n     * Model of the device the end user of the application is using. Used for client scenarios. If this field is empty then it is derived from the user agent.\r\n     */\r\n    public deviceModel: string;\r\n\r\n    /**\r\n     * Client device OEM name taken from the browser.\r\n     */\r\n    public deviceOEMName: string;\r\n\r\n    /**\r\n     * Operating system name and version of the device the end user of the application is using. If this field is empty then it is derived from the user agent. Example 'Windows 10 Pro 10.0.10586.0'\r\n     */\r\n    public deviceOSVersion: string;\r\n\r\n    /**\r\n     * The type of the device the end user of the application is using. Used primarily to distinguish JavaScript telemetry from server side telemetry. Examples: 'PC', 'Phone', 'Browser'. 'PC' is the default value.\r\n     */\r\n    public deviceType: string;\r\n\r\n    /**\r\n     * The IP address of the client device. IPv4 and IPv6 are supported. Information in the location context fields is always about the end user. When telemetry is sent from a service, the location context is about the user that initiated the operation in the service.\r\n     */\r\n    public locationIp: string;\r\n\r\n    /**\r\n     * A unique identifier for the operation instance. The operation.id is created by either a request or a page view. All other telemetry sets this to the value for the containing request or page view. Operation.id is used for finding all the telemetry items for a specific operation instance.\r\n     */\r\n    public operationId: string;\r\n\r\n    /**\r\n     * The name (group) of the operation. The operation.name is created by either a request or a page view. All other telemetry items set this to the value for the containing request or page view. Operation.name is used for finding all the telemetry items for a group of operations (i.e. 'GET Home/Index').\r\n     */\r\n    public operationName: string;\r\n\r\n    /**\r\n     * The unique identifier of the telemetry item's immediate parent.\r\n     */\r\n    public operationParentId: string;\r\n\r\n    /**\r\n     * Name of synthetic source. Some telemetry from the application may represent a synthetic traffic. It may be web crawler indexing the web site, site availability tests or traces from diagnostic libraries like Application Insights SDK itself.\r\n     */\r\n    public operationSyntheticSource: string;\r\n\r\n    /**\r\n     * The correlation vector is a light weight vector clock which can be used to identify and order related events across clients and services.\r\n     */\r\n    public operationCorrelationVector: string;\r\n\r\n    /**\r\n     * Session ID - the instance of the user's interaction with the app. Information in the session context fields is always about the end user. When telemetry is sent from a service, the session context is about the user that initiated the operation in the service.\r\n     */\r\n    public sessionId: string;\r\n\r\n    /**\r\n     * Boolean value indicating whether the session identified by ai.session.id is first for the user or not.\r\n     */\r\n    public sessionIsFirst: string;\r\n\r\n    /**\r\n     * In multi-tenant applications this is the account ID or name which the user is acting with. Examples may be subscription ID for Azure portal or blog name blogging platform.\r\n     */\r\n    public userAccountId: string;\r\n\r\n    /**\r\n     * Anonymous user id. Represents the end user of the application. When telemetry is sent from a service, the user context is about the user that initiated the operation in the service.\r\n     */\r\n    public userId: string;\r\n\r\n    /**\r\n     * Authenticated user id. The opposite of ai.user.id, this represents the user with a friendly name. Since it's PII information it is not collected by default by most SDKs.\r\n     */\r\n    public userAuthUserId: string;\r\n\r\n    /**\r\n     * Name of the role the application is a part of. For Azure environment, this should be initialized with \r\n     * [Microsoft.WindowsAzure.ServiceRuntime.RoleEnvironment]::CurrentRoleInstance.Role.Name\r\n     * See more details here: https://dzone.com/articles/accessing-azure-role-0\r\n     * It is recommended that you initialize environment variable with this value during machine startup, and then set context field from environment variable\r\n     * appInsights.client.context.tags[appInsights.client.context.keys.cloudRole] = process.env.RoleName\r\n     */\r\n    public cloudRole: string;\r\n\r\n    /**\r\n     * Name of the instance where the application is running. For Azure environment, this should be initialized with \r\n     * [Microsoft.WindowsAzure.ServiceRuntime.RoleEnvironment]::CurrentRoleInstance.Id\r\n     * See more details here: https://dzone.com/articles/accessing-azure-role-0\r\n     * It is recommended that you initialize environment variable with this value during machine startup, and then set context field from environment variable\r\n     * appInsights.client.context.tags[appInsights.client.context.keys.cloudRoleInstance] = process.env.RoleInstanceId\r\n     */\r\n    public cloudRoleInstance: string;\r\n\r\n\r\n    /**\r\n     * SDK version. See https://github.com/microsoft/ApplicationInsights-Home/blob/master/SDK-AUTHORING.md#sdk-version-specification for information.\r\n     */\r\n    public internalSdkVersion: string;\r\n\r\n    /**\r\n     * Agent version. Used to indicate the version of StatusMonitor installed on the computer if it is used for data collection.\r\n     */\r\n    public internalAgentVersion: string;\r\n\r\n    /**\r\n     * This is the node name used for billing purposes. Use it to override the standard detection of nodes.\r\n     */\r\n    public internalNodeName: string;\r\n\r\n    constructor() {\r\n        this.applicationVersion = \"ai.application.ver\";\r\n        this.deviceId = \"ai.device.id\";\r\n        this.deviceLocale = \"ai.device.locale\";\r\n        this.deviceModel = \"ai.device.model\";\r\n        this.deviceOEMName = \"ai.device.oemName\";\r\n        this.deviceOSVersion = \"ai.device.osVersion\";\r\n        this.deviceType = \"ai.device.type\";\r\n        this.locationIp = \"ai.location.ip\";\r\n        this.operationId = \"ai.operation.id\";\r\n        this.operationName = \"ai.operation.name\";\r\n        this.operationParentId = \"ai.operation.parentId\";\r\n        this.operationSyntheticSource = \"ai.operation.syntheticSource\";\r\n        this.operationCorrelationVector = \"ai.operation.correlationVector\";\r\n        this.sessionId = \"ai.session.id\";\r\n        this.sessionIsFirst = \"ai.session.isFirst\";\r\n        this.userAccountId = \"ai.user.accountId\";\r\n        this.userId = \"ai.user.id\";\r\n        this.userAuthUserId = \"ai.user.authUserId\";\r\n        this.cloudRole = \"ai.cloud.role\";\r\n        this.cloudRoleInstance = \"ai.cloud.roleInstance\";\r\n        this.internalSdkVersion = \"ai.internal.sdkVersion\";\r\n        this.internalAgentVersion = \"ai.internal.agentVersion\";\r\n        this.internalNodeName = \"ai.internal.nodeName\";\r\n    }\r\n}\r\nexport = ContextTagKeys;\r\n"]}