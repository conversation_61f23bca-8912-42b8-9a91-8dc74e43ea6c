import { Telemetry } from "./Telemetry";
/**
 * Telemetry type used for availability web test results.
 */
export interface AvailabilityTelemetry extends Telemetry {
    /**
     * Identifier of a test run. Use it to correlate steps of test run and telemetry generated by the service.
     */
    id: string;
    /**
     * Name of the test that these availability results represent.
     */
    name: string;
    /**
     * Request duration in ms
     */
    duration: number;
    /**
     * Success flag.
     */
    success: boolean;
    /**
     * Name of the location where the test was run from.
     */
    runLocation: string;
    /**
     * Diagnostic message for the result.
     */
    message: string;
    /**
     * Metrics associated with this event, displayed in Metrics Explorer on the portal.
     */
    measurements?: {
        [key: string]: number;
    };
}
