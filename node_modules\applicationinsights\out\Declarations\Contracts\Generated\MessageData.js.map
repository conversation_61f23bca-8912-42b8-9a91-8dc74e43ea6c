{"version": 3, "file": "MessageData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/MessageData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AAEpC,YAAY,CAAC;AAET;;GAEG;AACH;IAA0B,+BAAM;IAuB5B;QAAA,YAEI,iBAAO,SAIV;QAFG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;;IACzB,CAAC;IACL,kBAAC;AAAD,CAAC,AA9BD,CAA0B,MAAM,GA8B/B;AACL,iBAAS,WAAW,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\nimport SeverityLevel = require('./SeverityLevel');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Instances of Message represent printf-like trace statements that are text-searched. Log4Net, NLog and other text-based log file entries are translated into intances of this type. The message does not have measurements.\r\n     */\r\n    class MessageData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Trace message\r\n         */\r\n        public message: string;\r\n        \r\n        /**\r\n         * Trace severity level.\r\n         */\r\n        public severityLevel: SeverityLevel;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.properties = {};\r\n        }\r\n    }\r\nexport = MessageData;\r\n"]}