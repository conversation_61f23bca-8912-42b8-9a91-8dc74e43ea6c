{"version": 3, "file": "NodeHttpRequestTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/NodeHttpRequestTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\nimport http = require(\"http\");\r\n\r\n/**\r\n * Object encapsulating information about the incoming HTTP request\r\n */\r\nexport interface NodeHttpRequestTelemetry extends Telemetry\r\n{\r\n    /**\r\n     * HTTP request object\r\n     */\r\n    request: http.IncomingMessage;\r\n\r\n    /**\r\n     * HTTP response object\r\n     */\r\n    response: http.ServerResponse;\r\n    \r\n    /**\r\n     * HTTP request duration. Used only for synchronous tracks.\r\n     */\r\n    duration?: number;\r\n\r\n    /**\r\n     * Error that occurred while processing the request. Used only for synchronous tracks.\r\n     */\r\n    error?: any\r\n}"]}