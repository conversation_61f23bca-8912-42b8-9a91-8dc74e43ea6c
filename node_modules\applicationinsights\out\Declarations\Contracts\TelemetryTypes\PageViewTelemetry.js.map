{"version": 3, "file": "PageViewTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/PageViewTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry type used for availability web test results.\r\n */\r\nexport interface PageViewTelemetry extends Telemetry {\r\n\r\n        /**\r\n         * Name of the test that these availability results represent.\r\n         */\r\n        name?: string;\r\n\r\n        /**\r\n         * URL of the page to track.\r\n         */\r\n        url?: string;\r\n\r\n        /**\r\n         * Request duration in ms\r\n         */\r\n        duration?: number;\r\n\r\n        /**\r\n         * Metrics associated with this event, displayed in Metrics Explorer on the portal.\r\n         */\r\n        measurements?: { [key: string]: number; };\r\n}"]}