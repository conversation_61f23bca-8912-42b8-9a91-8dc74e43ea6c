{"version": 3, "file": "ExceptionData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/ExceptionData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AAGpC,YAAY,CAAC;AAET;;GAEG;AACH;IAA4B,iCAAM;IAiC9B;QAAA,YAEI,iBAAO,SAMV;QAJG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,oBAAC;AAAD,CAAC,AA1CD,CAA4B,MAAM,GA0CjC;AACL,iBAAS,aAAa,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\nimport ExceptionDetails = require('./ExceptionDetails');\r\nimport SeverityLevel = require('./SeverityLevel');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * An instance of Exception represents a handled or unhandled exception that occurred during execution of the monitored application.\r\n     */\r\n    class ExceptionData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Exception chain - list of inner exceptions.\r\n         */\r\n        public exceptions: ExceptionDetails[];\r\n        \r\n        /**\r\n         * Severity level. Mostly used to indicate exception severity level when it is reported by logging library.\r\n         */\r\n        public severityLevel: SeverityLevel;\r\n        \r\n        /**\r\n         * Identifier of where the exception was thrown in code. Used for exceptions grouping. Typically a combination of exception type and a function from the call stack.\r\n         */\r\n        public problemId: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.exceptions = [];\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = ExceptionData;\r\n"]}