{"version": 3, "file": "MetricData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/MetricData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AAEpC,YAAY,CAAC;AAET;;GAEG;AACH;IAAyB,8BAAM;IAkB3B;QAAA,YAEI,iBAAO,SAKV;QAHG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;;IACzB,CAAC;IACL,iBAAC;AAAD,CAAC,AA1BD,CAAyB,MAAM,GA0B9B;AACL,iBAAS,UAAU,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\nimport DataPoint = require('./DataPoint');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * An instance of the Metric item is a list of measurements (single data points) and/or aggregations.\r\n     */\r\n    class MetricData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * List of metrics. Only one metric in the list is currently supported by Application Insights storage. If multiple data points were sent only the first one will be used.\r\n         */\r\n        public metrics: DataPoint[];\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.metrics = [];\r\n            this.properties = {};\r\n        }\r\n    }\r\nexport = MetricData;\r\n"]}