{"version": 3, "file": "EventData.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/EventData.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,8BAA8B;AAC9B,iCAAoC;AACpC,YAAY,CAAC;AAET;;GAEG;AACH;IAAwB,6BAAM;IAuB1B;QAAA,YAEI,iBAAO,SAKV;QAHG,KAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,YAAY,GAAG,EAAE,CAAC;;IAC3B,CAAC;IACL,gBAAC;AAAD,CAAC,AA/BD,CAAwB,MAAM,GA+B7B;AACL,iBAAS,SAAS,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport Domain = require('./Domain');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Instances of Event represent structured event records that can be grouped and searched by their properties. Event data item also creates a metric of event count by name.\r\n     */\r\n    class EventData extends Domain\r\n    {\r\n        \r\n        /**\r\n         * Schema version\r\n         */\r\n        public ver: number;\r\n        \r\n        /**\r\n         * Event name. Keep it low cardinality to allow proper grouping and useful metrics.\r\n         */\r\n        public name: string;\r\n        \r\n        /**\r\n         * Collection of custom properties.\r\n         */\r\n        public properties: any;\r\n        \r\n        /**\r\n         * Collection of custom measurements.\r\n         */\r\n        public measurements: any;\r\n        \r\n        constructor()\r\n        {\r\n            super();\r\n            \r\n            this.ver = 2;\r\n            this.properties = {};\r\n            this.measurements = {};\r\n        }\r\n    }\r\nexport = EventData;\r\n"]}