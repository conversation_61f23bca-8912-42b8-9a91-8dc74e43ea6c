import Domain = require('./Domain');
/**
 * Instances of AvailabilityData represent the result of executing an availability test.
 */
declare class AvailabilityData extends Domain {
    /**
     * Schema version
     */
    ver: number;
    /**
     * Identifier of a test run. Use it to correlate steps of test run and telemetry generated by the service.
     */
    id: string;
    /**
     * Name of the test that these availability results represent.
     */
    name: string;
    /**
     * Duration in format: DD.HH:MM:SS.MMMMMM. Must be less than 1000 days.
     */
    duration: string;
    /**
     * Success flag.
     */
    success: boolean;
    /**
     * Name of the location where the test was run from.
     */
    runLocation: string;
    /**
     * Diagnostic message for the result.
     */
    message: string;
    /**
     * Collection of custom properties.
     */
    properties: any;
    /**
     * Collection of custom measurements.
     */
    measurements: any;
    constructor();
}
export = AvailabilityData;
