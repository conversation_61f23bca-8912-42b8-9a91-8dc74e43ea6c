{"version": 3, "file": "ExceptionDetails.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/ExceptionDetails.ts"], "names": [], "mappings": "AAEA,YAAY,CAAC;AAET;;GAEG;AACH;IAsCI;QAEI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IACL,uBAAC;AAAD,CAAC,AA3CD,IA2CC;AACL,iBAAS,gBAAgB,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\nimport StackFrame = require('./StackFrame');\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Exception details of the exception in a chain.\r\n     */\r\n    class ExceptionDetails\r\n    {\r\n        \r\n        /**\r\n         * In case exception is nested (outer exception contains inner one), the id and outerId properties are used to represent the nesting.\r\n         */\r\n        public id: number;\r\n        \r\n        /**\r\n         * The value of outerId is a reference to an element in ExceptionDetails that represents the outer exception\r\n         */\r\n        public outerId: number;\r\n        \r\n        /**\r\n         * Exception type name.\r\n         */\r\n        public typeName: string;\r\n        \r\n        /**\r\n         * Exception message.\r\n         */\r\n        public message: string;\r\n        \r\n        /**\r\n         * Indicates if full exception stack is provided in the exception. The stack may be trimmed, such as in the case of a StackOverflow exception.\r\n         */\r\n        public hasFullStack: boolean;\r\n        \r\n        /**\r\n         * Text describing the stack. Either stack or parsedStack should have a value.\r\n         */\r\n        public stack: string;\r\n        \r\n        /**\r\n         * List of stack frames. Either stack or parsedStack should have a value.\r\n         */\r\n        public parsedStack: StackFrame[];\r\n        \r\n        constructor()\r\n        {\r\n            this.hasFullStack = true;\r\n            this.parsedStack = [];\r\n        }\r\n    }\r\nexport = ExceptionDetails;\r\n"]}