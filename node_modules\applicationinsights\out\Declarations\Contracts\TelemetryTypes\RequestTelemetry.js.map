{"version": 3, "file": "RequestTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/RequestTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\n\r\n/**\r\n * Telemetry about the incoming request processed by the application\r\n */\r\nexport interface RequestTelemetry extends Telemetry\r\n{\r\n     /**\r\n      * Request name\r\n      */\r\n     name: string;\r\n\r\n     /**\r\n      * Request url\r\n      */\r\n     url: string;\r\n\r\n     /**\r\n      * Request source. This encapsulates the information about the component that initiated the request\r\n      */\r\n     source?: string;\r\n\r\n     /**\r\n      * Request duration in ms\r\n      */\r\n     duration: number;\r\n\r\n     /**\r\n      * Result code reported by the application\r\n      */\r\n     resultCode: string | number;\r\n\r\n     /**\r\n      * Whether the request was successful\r\n      */\r\n     success: boolean;\r\n}"]}