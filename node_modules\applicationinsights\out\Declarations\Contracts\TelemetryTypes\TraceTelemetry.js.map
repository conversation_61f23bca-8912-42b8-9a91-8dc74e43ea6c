{"version": 3, "file": "TraceTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/TraceTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\nimport Contracts = require(\"../\");\r\n\r\n/**\r\n * Trace telemetry reports technical, usually detailed information about the environment, \r\n * usage of resources, performance, capacity etc\r\n */\r\nexport interface TraceTelemetry extends Telemetry {\r\n    /**\r\n     * Trace message\r\n     */\r\n    message: string;\r\n    /**\r\n     * Trace severity level\r\n     */\r\n    severity?: Contracts.SeverityLevel;\r\n}"]}