{"version": 3, "file": "EnvelopeQuickPulse.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/QuickPulseTypes/EnvelopeQuickPulse.ts"], "names": [], "mappings": "", "sourcesContent": ["import { DocumentQuickPulse } from \"./DocumentQuickPulse\";\r\nimport { MetricQuickPulse } from \"./MetricQuickPulse\";\r\n\r\nexport interface EnvelopeQuickPulse {\r\n    Documents: DocumentQuickPulse[];\r\n\r\n    Instance: string;\r\n\r\n    InstrumentationKey: string;\r\n\r\n    InvariantVersion: number;\r\n\r\n    MachineName: string;\r\n\r\n    Metrics: MetricQuickPulse[];\r\n\r\n    StreamId: string;\r\n\r\n    Timestamp: string;\r\n\r\n    Version: string;\r\n}\r\n"]}