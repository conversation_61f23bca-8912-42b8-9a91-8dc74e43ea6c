{"version": 3, "file": "ExceptionTelemetry.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/TelemetryTypes/ExceptionTelemetry.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Telemetry }  from \"./Telemetry\";\r\nimport Contracts = require(\"../\");\r\n\r\n/**\r\n * Telemetry about the exception thrown by the application\r\n */\r\nexport interface ExceptionTelemetry extends Telemetry\r\n{\r\n    /**\r\n     * Exception thrown\r\n     */\r\n     exception: Error;\r\n\r\n    /**\r\n     * Metrics associated with this exception, displayed in Metrics Explorer on the portal. Defaults to empty\r\n     */\r\n    measurements?: { [key: string]: number; };\r\n    /**\r\n     * Exception severity level\r\n     */\r\n    severity?: Contracts.SeverityLevel;\r\n}"]}