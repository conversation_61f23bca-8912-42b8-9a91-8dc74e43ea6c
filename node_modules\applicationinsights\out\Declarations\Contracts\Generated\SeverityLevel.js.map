{"version": 3, "file": "SeverityLevel.js", "sourceRoot": "", "sources": ["../../../../Declarations/Contracts/Generated/SeverityLevel.ts"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,YAAY,CAAC;AAET;;GAEG;AACH,IAAK,aAOJ;AAPD,WAAK,aAAa;IAEd,uDAAW,CAAA;IACX,+DAAe,CAAA;IACf,uDAAW,CAAA;IACX,mDAAS,CAAA;IACT,yDAAY,CAAA;AAChB,CAAC,EAPI,aAAa,KAAb,aAAa,QAOjB;AACL,iBAAS,aAAa,CAAC", "sourcesContent": ["// THIS FILE WAS AUTOGENERATED\r\n\"use strict\";\r\n    \r\n    /**\r\n     * Defines the level of severity for the event.\r\n     */\r\n    enum SeverityLevel\r\n    {\r\n        Verbose = 0,\r\n        Information = 1,\r\n        Warning = 2,\r\n        Error = 3,\r\n        Critical = 4,\r\n    }\r\nexport = SeverityLevel;\r\n"]}